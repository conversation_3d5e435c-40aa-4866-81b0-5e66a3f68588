{"nodes": {"group": {"base": "Basic", "data_processing": "Data Processing", "data_acquisition": "Data Acquisition", "ai_model": "AI Model", "tool": "Tool", "plugin": "Plugin"}, "in": {"label": "Start", "nodeShowName": "Start (Input)", "helpDoc": "https://flowai.cc/tutorial/input/"}, "out": {"label": "End", "nodeShowName": "End (Output)", "helpDoc": "https://flowai.cc/tutorial/output/"}, "browser": {"label": "WebScraper", "nodeShowName": "Web Scraper", "helpDoc": "https://flowai.cc/tutorial/web-scraper/", "body": "Web content, like html, text, etc."}, "llm": {"label": "LLM", "nodeShowName": "LLM", "helpDoc": "https://flowai.cc/tutorial/llm/", "llm_output_desc": "LLM output"}, "llm_cls": {"label": "LLMIntent", "nodeShowName": "LLM Intent Classification", "helpDoc": "https://flowai.cc/tutorial/llm-intent/"}, "template": {"label": "Template", "nodeShowName": "Template", "helpDoc": "https://flowai.cc/tutorial/template/", "template_output_desc": "Template output"}, "adv_template": {"label": "AdvancedTemplate", "nodeShowName": "Advanced Template", "helpDoc": "https://flowai.cc/en/tutorial/advance-template/", "template_output_desc": "Template output"}, "http": {"label": "HttpCall", "nodeShowName": "HTTP Call", "helpDoc": "https://flowai.cc/tutorial/http/", "output_body_desc": "Web content", "output_status_desc": "Status code"}, "current_time": {"label": "CurrentTime", "nodeShowName": "Current Time", "helpDoc": "https://flowai.cc/tutorial/time/", "output_desc": "Return system current time, format like: 2024-01-02 03:04:05"}, "plugin": {"label": "CustomPlugin", "nodeShowName": "Custom Plugin", "group": "Plugin"}, "code_runner": {"label": "CodeRunner", "nodeShowName": "Code Runner", "helpDoc": "https://flowai.cc/tutorial/code-runner/", "output_desc": "Code execution result"}, "json_param_extract": {"label": "JSONParamExtraction", "nodeShowName": "JSON Parameter Extraction", "helpDoc": "https://flowai.cc/tutorial/json-extract/", "output_desc": "Extracted JSON parameters"}, "condition": {"label": "Condition", "nodeShowName": "Condition", "helpDoc": "https://flowai.cc/tutorial/condition/"}, "condition_class": {"label": "ConditionClassification", "nodeShowName": "Condition Classification", "output_desc": "Classification information"}, "loop": {"label": "Loop", "nodeShowName": "Loop", "helpDoc": "", "output_desc": "Loop execution result, JSON array"}, "loop_start": {"label": "LoopStart", "nodeShowName": "Loop Start", "helpDoc": "", "loop_start_item_desc": "Loop item", "loop_start_index_desc": "Loop index"}, "loop_end": {"label": "LoopEnd", "nodeShowName": "Loop End", "helpDoc": "", "loop_end_export_var_desc": "$LoopStart.item"}, "json_pretty": {"label": "JSONPretty", "nodeShowName": "JSON Pretty", "helpDoc": "https://flowai.cc/tutorial/json-format/", "output_desc": "Formatted JSON"}, "llm_agent": {"label": "LLMAgent", "nodeShowName": "LLM Agent", "helpDoc": "https://flowai.cc/en/tutorial/llm-agent/", "output_desc": "LLM Agent output result"}}, "condition_types": {"equals": "Equals", "not_equals": "Not equals", "contains": "Contains", "not_contains": "Not contains", "starts_with": "Starts with", "not_starts_with": "Starts with not equal", "ends_with": "Ends with", "not_ends_with": "Ends with not equal"}, "personal_settings": "Personal Settings", "settings": "Settings", "custom_llm": "Custom LLM", "credits": "Credits", "current_credits": "Current Credits", "user_info": "User Info", "user_identifier": "User Identifier", "email": "Email", "enter_email": "Please enter your email", "email_function_coming_soon": "Email modify is coming soon", "phone": "Phone", "enter_phone": "Please enter your phone", "new_password": "New Password", "enter_new_password": "Please enter your new password", "old_password": "Old Password", "enter_old_password": "Please enter your old password", "save_settings": "Save Settings", "no_changes": "No changes made", "old_password_required": "Old password is required when changing password", "update_success": "Update successful", "update_failed": "Update failed", "existing_custom_llm": "Existing Custom LLM", "edit_custom_llm": "Edit Custom LLM", "model_name": "Model Name", "model_protocol": "Protocol", "model_endpoint": "Model Endpoint", "model_key": "API Key", "support_function_call": "Support Function Call", "support_json_output": "Support JSON Output", "support_vision": "Support Vision", "add_custom_llm": "Add Custom LLM", "coming_soon": "Coming Soon", "stay_tuned": "Stay Tuned", "usernameOrEmail": "Username/Email", "login_password": "Password", "recharge": "Recharge", "login": "<PERSON><PERSON>", "or": "Or", "enter_username_or_email_and_password": "Please enter username/email and password", "error": "Error", "create-copy-failed": "Failed to create copy", "delete-failed": "Delete failed", "operation-failed": "Operation failed", "github-login-error": "Gith<PERSON> login failed: ", "google-login-error": "Google login failed: ", "login-comming-soon": "Registration will be available soon, stay tuned!", "login_error": "<PERSON><PERSON> failed: ", "login_with_github": "Login with <PERSON><PERSON><PERSON>", "login_with_google": "Login with Google", "search-workflow": "Search Workflow", "searching": "Searching...", "run": "Run", "edit": "Edit", "create-workflow": "Create Workflow", "run_logs": "Run Logs", "credits_history": "Credits History", "credits_history_desc": "View your credit consumption details and usage statistics", "llm_model": "LLM Model", "input_tokens": "Input Tokens", "output_tokens": "Output Tokens", "total_tokens": "Total Tokens", "total_credits_used": "Total Credits Used", "total_tokens_used": "Total Tokens Used", "total_records": "Total Records", "just_now": "Just now", "hours_ago": "hours ago", "yesterday": "Yesterday", "no_credits_history": "No credits consumption records", "no_credits_history_desc": "When you use LLM models, consumption records will be displayed here", "workflow": "Workflow", "create-copy": "Copy", "delete": "Delete", "confirm-logout": "Confirm logging out?", "click-button-to-start-automation": "Click the button below to start your first AI workflow", "confirm-delete-project": "Are you sure you want to delete the item?", "create-blank-workflow": "Create a blank AI workflow", "example": "Example", "logic-flow": "Logical Orchestration", "llm-data-processing-description": "Use LLM for data cleaning, transformation, and analysis by leveraging its powerful NLP capabilities. ", "llm-data-processing": "LLM data processing", "user": "User", "integrate-multiple-llm-models": "Integrate multiple LLM models to enable intelligent decision-making and content generation.", "unnamed-project": "Unnamed Projects", "use-this-example": "Use this example", "or-start-from-example": "Or start with the following example", "not-set": "Not set", "no-search-result": "No search results found", "no-projects": "No project yet", "no-description": "No description yet", "multi-llm-assistant": "Multi-LLM agent", "logout": "Logout", "visual-logic-flow": "Visualization logic orchestration, based on LLM to achieve complex business process automation.", "confirm-delete-log": "Are you sure you want to delete the log?", "delete-log-failed": "Delete log failed", "run-logs": "Run Logs", "show-logs-in-last-15-days": "Show logs in the last 15 days", "no-logs": "No logs yet", "view": "View", "close": "Close", "loading": "Loading", "success": "Success", "return": "Return", "save": "Save", "debug": "Debug", "content-not-saved-leave": "Content not saved, leave?", "save-error": "Save error", "save-success": "Save success", "loading-error": "Loading error", "current-workflow-has-no-output-node-confirm": "The current workflow has no output node, confirm to save?", "project-name-cannot-be-empty": "Project name cannot be empty", "project-name": "Project name:", "project-description": "Project description:", "none": "None", "delete-node": "Delete node", "node-config": "Node Config", "help-doc": "Help document", "copy": "Copy", "paste": "Paste", "undo": "Undo", "start": "Start", "runtime": "Runtime", "copy_success": "Copy success", "copied_to_clipboard": "Copied to clipboard", "input": "Input", "output": "Output", "seconds": "seconds", "copy_output": "Copy output", "input_value": "Input value", "use_variable_with_slash": "You can use variables by entering <span class=\"bg-base-200 px-1 py-0.5 rounded\">/</span>.", "no_input_data": "- No input data -", "collapse": "Collapse", "expand": "Expand", "node_name_cannot_be_empty": "Node name cannot be empty, please re-enter", "node_name_already_exists": "Node name \"{name}\" already exists, please re-enter", "search": "Search...", "upload-success": "Upload success", "stop-exec": "Stop execution", "start-exec": "Start execution", "pls-select": "Please select", "warnning-no-output-node": "Warning: The current workflow has no \"End (Output)\" node, so it cannot run normally.", "duration": "Duration", "running_time": "Running time", "pls-select-valid-option": "Please select a valid option", "pls-select-at-least-one-option": "Please select at least one option", "pls-upload-image": "Please upload image", "max-upload-image": "Max upload {count} images", "image-upload-progress": "Image upload progress", "uploaded-image-progress": "Uploaded {uploadedImages}/{totalImages} images", "image-upload-status": "Image upload status", "start-upload": "Start upload", "img_upload_err": "Image upload failed:", "debug_err": "Debug error:", "run_err": "Run error:", "preview-img": "Preview image", "support-jpg-png-gif-format": "Support jpg, png, gif format, single file size cannot exceed 5MB", "continue-upload": "Continue upload", "remaining-upload-count": "(Remaining upload count: {count})", "click-upload": "Click upload", "drag-image-here": "Drag image here or", "upload-invalid-image-format": "Please upload jpg, png or gif format image", "image-size-exceeds-limit": "Image size cannot exceed 5MB", "image-processing-failed": "Image processing failed, please try again", "workflow-edit": "Workflow Edit", "runtime-workflow": "Runtime Workflow", "default": "<PERSON><PERSON><PERSON>", "text": "Text", "longtext": "Long Text", "select": "Select", "radio": "Radio", "image": "Image", "checkbox": "Checkbox", "type": "Type", "name": "Name", "node_name": "Node Name", "add_input_variable": "Add input variable", "input_variable": "Input Variable", "options": "Options", "add_option": "Add Option", "add_new_variable": "Add New Variable", "default_value": "Default Value", "default_1_image": "Default 1 image", "allow_upload_image_number": "Allow upload image number", "drag_to_reorder": "Drag to reorder", "option_value": "Option Value", "at_least_one_option": "At least one option is required", "browser_url": "Browser URL", "from_variable": "From variable", "direct_input": "Direct input", "input_url": "Input URL", "code_runner_desc": "*Currently only supports running JavaScript code, the code will run in the <b>Node 18 environment without internet connection</b> environment. The maximum execution time is 30 seconds (including container build time).", "get_from_context": "Get from context", "manual_input": "Manual input", "variable_name": "Variable name", "input_params": "Input params", "add": "Add", "js_code": "JS code", "key_must_be_string": "Key name must be a string type.", "invalid_variable_name": "Invalid variable name. Please use letters, numbers, and underscores, and cannot start with a number.", "select_condition_content": "Select the content to judge", "set_condition": "Set condition config", "condition_options": "Condition options", "please_add_condition_options": "Please add condition options", "add_condition": "Add condition", "content": "Content", "condition_content": "Condition content", "request_body": "Request body", "request_headers_empty": "Request headers are empty, if you need to add request headers, please click the button above", "request_headers": "Request headers", "http_address": "HTTP address", "or_the_first_element_of_the_array": "or the first element of the array", "for_example": "for example", "json_data_source": "JSON Data Source", "json_path": "JSON Path", "pls-select-json-data-source": "Please select data source", "model": "Model", "context_image": "Context image", "not_use_image": "Not use image", "prompt": "Prompt", "system_prompt": "System prompt", "more_config": "More config", "json_output": "JSON Output", "enable_json_output_warning": "After enabling JSON output, you need to clearly require the LLM to output JSON data in the prompt", "max_output_length": "Max output length", "max_output_length_warning": "Control the maximum token number of the model output, default 0 means no limit", "set_llm_classifier_class": "Set LLM classification category", "select_judgment_content": "Select the content to judge", "classifier_class": "Classification category", "please_add_class": "Please add category", "class_name": "class name", "class": "class", "can_be_empty": "Can be empty, if empty, use the default classification prompt", "classifier_prompt": "Classification prompt", "can_use_variables": "Can use variables, such as: <span class=\"bg-base-200 px-1 py-0.5 rounded\">$input.input</span>", "loop": "Loop", "loop_var": "Loop variable", "please_input_node_name": "Please input node name", "please_select_loop_var": "Please select loop variable", "loop_var_desc": "The variable to be looped must be an array type (as long as it can be parsed as an array by JSON), for example: [\"a\", \"b\", \"c\"], the number of loops is the length of the array. If it is not an array type, for example, the variable content is \"a\", it will be regarded as [\"a\"], and the loop will only execute once. <span class=\"font-bold\" >The loop will execute at most 500 times, if the loop times exceed 500 times, the loop will stop.</span>", "loop_sleep": "Loop sleep duration", "loop_sleep_desc": "Unit: seconds, default 0 seconds means that the loop will immediately start the next loop after the loop ends, without waiting. If set to a value greater than 0, it means that there will be a sleep wait between 2 loops to prevent high-frequency requests.", "no_output": "No output", "no_results_yet": "No results yet", "click_start_to_run": "Click the Start button to run the flow", "export_var": "Export variable", "please_select_export_var": "Please select the variable to export", "end_loop_once": "End loop once", "export_content": "Export content", "start_loop_once": "Start loop once", "each_element_will_execute_once": "Each element will execute once", "loop_start": "Loop start", "loop_end": "Loop end", "select_output_content": "Select output content", "custom_plugin": "Custom plugin", "return_system_time": "Return system time", "failed_to_create_canvas_context": "Failed to create canvas context", "failed_to_compress_image": "Failed to compress image", "failed_to_load_image": "Failed to load image", "failed_to_upload_file": "Failed to upload file", "enter_model_key": "Enter API Key", "get_custom_llm_error": "get custom LLM error", "add_custom_llm_success": "add custom llm success", "add_custom_llm_error": "add custom LLM error", "confirm_delete_custom_llm": "Are you sure you want to delete this custom LLM?", "delete_custom_llm_success": "Delete custom LLM successfully", "delete_custom_llm_error": "Failed to delete a custom LLM", "update_custom_llm_success": "Updated custom LLM successfully", "update_custom_llm_error": "Failed to update custom LLM", "cancel": "Cancel", "save_edit": "Save Edit", "endpoint_placeholder": "Enter the API endpoint, for example https://api.openai.com/v1", "support_model_type_desc": "* Currently we only support OpenAI protocol, Anthropic protocol, and OpenAI-compatible protocol (such as deepseek).", "must_enter_old_password": "When modifying the password, you must enter the old password", "update_error": "Update failed", "no_variable": "No Variable", "click_add_to_start": "click \"add\" to start", "go_template_example": "<li> Varriable:  <code> &#123;&#123; .varName &#125;&#125;</code></li><li>Condition: <code> &#123;&#123;if .condition&#125;&#125;...&#123;&#123;else&#125;&#125;...&#123;&#123; end &#125;&#125;</code></li><li>Loop: <code> &#123;&#123;range .items&#125;&#125;...&#123;&#123; end &#125;&#125;</code></li><li>Pipe: <code> &#123;&#123; .value | upper | lower &#125;&#125;</code></li><li>Function: <code> &#123;&#123; json_parse .value &#125;&#125;</code></li>", "go_template_example_title": "Go Template Example:", "variable": "Variable", "condition": "Condition", "pipe": "Pipeline", "function": "Function", "payment_error": "Recharge failed, please try again later", "confirm_payment": "Go to Pay", "price": "Price", "points_to_add": "Credits", "recharge_confirmation": "Recharge confirmation", "redirecting_to_payment": "Redirecting to payment....", "context_limit": "Context Limit", "context_limit_description": "LLM Context limit (K)", "publish_api": "Publish API", "versions": "versions", "publish-workflow": "Publish Workflow", "failed-to-load-published-version": "Read version error", "confirm-delete-version": "Confirm the delete version", "delete-version-success": "Delete the version successfully", "publish-error": "Publish workflow error", "publish-success": "Publish workflow success", "delete-version-error": "Removal version error", "publish-current-version": "Publish the current version", "published-versions": "Published Versions", "no-published-versions": "No published version yet", "api_management": "API Management", "api_keys": "API Keys", "create_new_key": "Create New Key", "key": "Key", "created_at": "Created At", "expires_at": "Expires At", "last_used": "Last Used", "actions": "Actions", "no_api_keys": "No API Keys", "never": "Never", "published_workflows": "Published Workflows", "version": "Version", "description": "Description", "status": "Status", "no_description": "No Description", "update_status": "Update Status", "create_new_api_key": "Create New API Key", "key_name": "Key Name", "enter_key_name": "Enter key name", "expires_in_days": "Expires In Days", "creating": "Creating", "create": "Create", "api_key_created": "API Key Created", "key_warning": "Note: This key can directly execute your workflow, please save it properly!", "api_key": "API Key", "done": "Done", "update_workflow_status": "Update Workflow Status", "current_status": "Current Status", "new_status": "New Status", "updating": "Updating", "update": "Update", "no_published_workflows": "No Published Workflows", "confirm_delete": "Confirm Delete", "confirm_delete_api_key": "Are you sure you want to delete this API key? This action cannot be undone.", "confirm_delete_workflow": "Are you sure you want to delete this published workflow? This action cannot be undone.", "confirm": "Confirm", "save-and-publish-current-workflow": "Save and publish the current workflow", "confirm-save-and-publish": "Are you sure you want to save and publish the current workflow? The current version will be saved and overwritten as the latest version.", "publish-warning-message": "Note: This action saves the current workflow and publishes it as the latest version.", "copy_to_clipboard_failed": "Copy to clipboard failed", "endpoint": "Endpoint", "workflow_id": "Workflow ID", "api_request_example": "API Request Example", "signup_msg": "Don't have an account?", "signup": "Sign Up", "login_msg": "Already have an account?", "signup_with_google": "Sign up with Google", "signup_with_github": "Sign up with <PERSON><PERSON><PERSON>", "register_success": "Registration Successful", "register_error": "Registration Failed", "please_fill_all_fields": "Please fill in all fields", "password_not_match": "Passwords do not match", "invalid_email": "Please enter a valid email address", "email_verification": "Email Verification", "verification_code": "Verification Code", "enter_verification_code": "Enter verification code", "verification_code_sent_to": "Verification code sent to", "verify_email": "<PERSON><PERSON><PERSON>", "verification_success": "Verification Successful", "verification_error": "Verification Failed", "email_verified": "Email has been verified", "didnt_receive_code": "Didn't receive a code?", "resend_code": "Resend Code", "resend_success": "Resend Successful", "resend_error": "Resend Failed", "back_to_login": "Back to Login", "please_enter_verification_code": "Please enter verification code", "verification_email_sent": "Verification email has been sent", "confirm_password": "Confirm Password", "credits_reward_info": "Credits Reward Information", "email_signup_credits": "Email registration: {credits} credits bonus", "oauth_signup_credits": "Google or Github login: {credits} credits bonus", "auto_verifying": "Auto-verifying your email...", "output_lang": "Output Language", "built_in_tools": "Built-in Tools", "max_iterations": "Max Iterations", "max_iterations_desc": "Maximum number of iterations for LLM Agent to execute tools", "advanced_settings": "Advanced Settings", "mcp_servers": "MCP Servers", "unnamed_server": "Unnamed Server", "server_name": "Server Name", "server_url": "Server URL", "server_type": "Server Type", "server_headers": "Custom HTTP Headers", "add_header": "<PERSON>d <PERSON>", "no_mcp_servers": "No MCP Server Configuration", "add_mcp_server": "Add MCP Server", "fetch_web": "Web Fetch", "http_call": "HTTP Call", "calculator": "Calculator", "custom_llm_management": "Custom LLM Management", "manage_your_custom_llm_models": "Manage your custom LLM models", "no_custom_llms": "No Custom LLMs", "no_custom_llms_desc": "You haven't added any custom LLM models yet. Click the button above to get started.", "basic_info": "Basic Information", "advanced_options": "Advanced Options", "tokens": "Tokens", "function_call": "Function Call", "vision": "Vision", "editing": "Editing", "confirm_delete_custom_llm_message": "Are you sure you want to delete model \"{name}\"?", "delete_warning_irreversible": "This action cannot be undone and the model will be permanently deleted.", "configure_new_llm_model": "Configure a new LLM model", "modify_llm_configuration": "Modify LLM configuration", "no_special_features": "No special features", "confirm_delete_item_message": "Are you sure you want to delete \"{name}\"?", "confirm_delete_default_message": "Are you sure you want to delete this item?", "editor": {"fullscreen": "Fullscreen Editor", "title": "Editor", "close": "Close"}, "reconnecting": "Reconnecting", "reconnect-success": "Reconnection successful", "recovered-from-log": "Recovered execution result from log", "reconnect-failed-max-attempts": "Reconnection failed: Maximum retry attempts reached", "reconnect-failed-error": "Reconnection failed: ", "network-error": "Network error", "attempting-reconnect": "Attempting to reconnect", "execution-failed": "Execution failed"}