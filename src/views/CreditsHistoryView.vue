<script setup lang="ts">
	import { useCurNav } from "@/stores/curNav";
	import { type CreditHistory, getCreditHistory } from "@/api/credits";
	import { ref, watch, onMounted, defineAsyncComponent, computed } from "vue";
	import {
		CurrencyDollarIcon,
		CpuChipIcon,
		ArrowTrendingDownIcon,
		ClockIcon,
		ChartBarIcon,
		SparklesIcon,
	} from "@heroicons/vue/24/outline";
	import component_wait from "@/components/utils/component_wait.vue";

	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	// 延迟加载组件
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));

	const curNavStore = useCurNav();
	curNavStore.setCurNav("credits_history");

	const credits = ref<CreditHistory[]>([]);
	const creditCount = ref(0);
	const page = ref(1);
	const pageSize = ref(20);
	const loading = ref(true);

	const load = () => {
		loading.value = true;
		getCreditHistory(page.value, pageSize.value)
			.then((res) => {
				credits.value = res.list;
				creditCount.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

		if (diffInHours < 1) {
			return t("just_now");
		} else if (diffInHours < 24) {
			return `${diffInHours} ${t("hours_ago")}`;
		} else if (diffInHours < 48) {
			return t("yesterday");
		} else {
			return date.toLocaleDateString();
		}
	};

	const formatCredits = (credits: number) => {
		return credits.toFixed(2);
	};

	const getModelIcon = (modelType: string) => {
		if (modelType.toLowerCase().includes("gpt")) {
			return SparklesIcon;
		} else if (modelType.toLowerCase().includes("claude")) {
			return CpuChipIcon;
		} else {
			return ChartBarIcon;
		}
	};

	const getModelColor = (modelType: string) => {
		if (modelType.toLowerCase().includes("gpt")) {
			return "from-emerald-400 to-emerald-600";
		} else if (modelType.toLowerCase().includes("claude")) {
			return "from-orange-400 to-orange-600";
		} else {
			return "from-blue-400 to-blue-600";
		}
	};

	// 计算统计数据
	const totalCreditsUsed = computed(() => {
		return credits.value.reduce((sum, credit) => sum + credit.credits, 0);
	});

	const totalTokensUsed = computed(() => {
		return credits.value.reduce(
			(sum, credit) => sum + credit.llm_input + credit.llm_output,
			0
		);
	});

	watch(page, () => {
		load();
	});

	onMounted(() => {
		load();
	});
</script>

<template>
	<div class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-full p-6">
		<!-- 页面标题和统计卡片 -->
		<div class="mb-8">
			<div class="flex items-center space-x-4 mb-6">
				<div
					class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg"
				>
					<CurrencyDollarIcon class="w-6 h-6 text-white" />
				</div>
				<div>
					<h1 class="text-3xl font-bold text-slate-800">
						{{ t("credits_history") }}
					</h1>
					<p class="text-slate-600 mt-1">{{ t("credits_history_desc") }}</p>
				</div>
			</div>

			<!-- 统计卡片 -->
			<div
				v-if="!loading && credits.length > 0"
				class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
			>
				<div
					class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-6"
				>
					<div class="flex items-center justify-between">
						<div>
							<p class="text-sm font-medium text-slate-600">
								{{ t("total_credits_used") }}
							</p>
							<p class="text-2xl font-bold text-red-600">
								{{ formatCredits(totalCreditsUsed) }}
							</p>
						</div>
						<div
							class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-xl flex items-center justify-center"
						>
							<ArrowTrendingDownIcon class="w-6 h-6 text-red-600" />
						</div>
					</div>
				</div>

				<div
					class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-6"
				>
					<div class="flex items-center justify-between">
						<div>
							<p class="text-sm font-medium text-slate-600">
								{{ t("total_tokens_used") }}
							</p>
							<p class="text-2xl font-bold text-blue-600">
								{{ totalTokensUsed.toLocaleString() }}
							</p>
						</div>
						<div
							class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center"
						>
							<ChartBarIcon class="w-6 h-6 text-blue-600" />
						</div>
					</div>
				</div>

				<div
					class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-6"
				>
					<div class="flex items-center justify-between">
						<div>
							<p class="text-sm font-medium text-slate-600">
								{{ t("total_records") }}
							</p>
							<p class="text-2xl font-bold text-green-600">
								{{ creditCount.toLocaleString() }}
							</p>
						</div>
						<div
							class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center"
						>
							<ClockIcon class="w-6 h-6 text-green-600" />
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 加载状态 -->
		<div v-if="loading" class="grid grid-cols-1 gap-4">
			<div
				v-for="i in 6"
				:key="i"
				class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 animate-pulse border border-slate-200"
			>
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-4">
						<div class="w-12 h-12 bg-slate-200 rounded-xl"></div>
						<div class="space-y-2">
							<div class="h-4 bg-slate-200 rounded w-24"></div>
							<div class="h-3 bg-slate-200 rounded w-16"></div>
						</div>
					</div>
					<div class="text-right space-y-2">
						<div class="h-6 bg-slate-200 rounded w-16"></div>
						<div class="h-3 bg-slate-200 rounded w-12"></div>
					</div>
				</div>
				<div class="mt-4 pt-4 border-t border-slate-200">
					<div class="grid grid-cols-2 gap-4">
						<div class="h-3 bg-slate-200 rounded"></div>
						<div class="h-3 bg-slate-200 rounded"></div>
					</div>
				</div>
			</div>
		</div>

		<!-- 积分历史列表 -->
		<div v-if="credits.length > 0 && !loading" class="grid grid-cols-1 gap-4">
			<div
				v-for="credit in credits"
				:key="credit.id"
				class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-200 hover:border-blue-300 overflow-hidden"
			>
				<div class="p-6">
					<div class="flex items-center justify-between mb-4">
						<div class="flex items-center space-x-4">
							<div
								:class="[
									'w-12 h-12 rounded-xl flex items-center justify-center shadow-md bg-gradient-to-br',
									getModelColor(credit.llm_type),
								]"
							>
								<component
									:is="getModelIcon(credit.llm_type)"
									class="w-6 h-6 text-white"
								/>
							</div>
							<div class="flex-grow min-w-0">
								<div class="flex items-center space-x-3 mb-2">
									<span class="text-sm font-medium text-slate-600">{{
										t("llm_model")
									}}</span>
									<span
										class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-slate-100 text-slate-800 border border-slate-200"
									>
										{{ credit.llm_type }}
									</span>
								</div>
								<div
									class="flex items-center space-x-2 text-sm text-slate-500"
								>
									<ClockIcon class="w-4 h-4" />
									<span>{{ formatDate(credit.created_at) }}</span>
								</div>
							</div>
						</div>
						<div class="text-right">
							<div class="text-2xl font-bold text-red-600 mb-1">
								-{{ formatCredits(credit.credits) }}
							</div>
							<div class="text-sm text-slate-500">{{ t("credits") }}</div>
						</div>
					</div>

					<!-- Token 使用详情 -->
					<div class="bg-slate-50/50 rounded-xl p-4 border border-slate-100">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="flex items-center justify-between">
								<div class="flex items-center space-x-2">
									<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
									<span class="text-sm font-medium text-slate-600">{{
										t("input_tokens")
									}}</span>
								</div>
								<span class="text-sm font-bold text-slate-800">{{
									credit.llm_input.toLocaleString()
								}}</span>
							</div>
							<div class="flex items-center justify-between">
								<div class="flex items-center space-x-2">
									<div class="w-2 h-2 bg-green-500 rounded-full"></div>
									<span class="text-sm font-medium text-slate-600">{{
										t("output_tokens")
									}}</span>
								</div>
								<span class="text-sm font-bold text-slate-800">{{
									credit.llm_output.toLocaleString()
								}}</span>
							</div>
						</div>
						<div class="mt-3 pt-3 border-t border-slate-200">
							<div class="flex items-center justify-between">
								<span class="text-sm font-medium text-slate-600">{{
									t("total_tokens")
								}}</span>
								<span class="text-sm font-bold text-slate-800">{{
									(credit.llm_input + credit.llm_output).toLocaleString()
								}}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 无数据状态 -->
		<div v-if="!loading && credits.length == 0" class="text-center py-16">
			<div class="max-w-md mx-auto">
				<div
					class="bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200 shadow-xl p-12"
				>
					<div
						class="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center mx-auto mb-6"
					>
						<CurrencyDollarIcon class="w-10 h-10 text-slate-400" />
					</div>
					<h3 class="text-2xl font-bold text-slate-800 mb-4">
						{{ t("no_credits_history") }}
					</h3>
					<p class="text-slate-600 leading-relaxed">
						{{ t("no_credits_history_desc") }}
					</p>
				</div>
			</div>
		</div>

		<!-- 分页 -->
		<div class="mt-8" v-if="creditCount > 0">
			<component_wait>
				<Page
					:page="page"
					:pageSize="pageSize"
					:total="creditCount"
					@change="(e) => (page = e)"
				></Page>
			</component_wait>
		</div>
	</div>
</template>

<style scoped>
	.slide-fade-enter-active,
	.slide-fade-leave-active {
		transition: all 0.5s ease;
	}
	.slide-fade-enter-from,
	.slide-fade-leave-to {
		transform: translateX(100%);
		opacity: 0;
	}
</style>
