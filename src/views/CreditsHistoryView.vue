<script setup lang="ts">
	import { useCurNav } from "@/stores/curNav";
	import { type CreditHistory, getCreditHistory } from "@/api/credits";
	import { ref, watch, onMounted, defineAsyncComponent } from "vue";
	import { C<PERSON>rencyDollarIcon, CpuChipIcon } from "@heroicons/vue/24/outline";
	import component_wait from "@/components/utils/component_wait.vue";

	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	// 延迟加载组件
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));

	const curNavStore = useCurNav();
	curNavStore.setCurNav("credits_history");

	const credits = ref<CreditHistory[]>([]);
	const creditCount = ref(0);
	const page = ref(1);
	const pageSize = ref(20);
	const loading = ref(true);

	const load = () => {
		loading.value = true;
		getCreditHistory(page.value, pageSize.value)
			.then((res) => {
				credits.value = res.list;
				creditCount.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString();
	};

	const formatCredits = (credits: number) => {
		return credits.toFixed(2);
	};

	watch(page, () => {
		load();
	});

	onMounted(() => {
		load();
	});
</script>

<template>
	<div class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-full p-6">
		<div class="relative bg-white rounded-xl overflow-hidden shadow-md box-border min-h-full p-5">
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<CurrencyDollarIcon class="w-8 h-8 text-primary" />
					<h1 class="text-2xl font-bold text-gray-800">{{ t("credits_history") }}</h1>
				</div>
			</div>

			<!-- 加载状态 -->
			<div v-if="loading" class="flex justify-center items-center py-12">
				<span class="loading loading-ring loading-lg"></span>
			</div>

			<!-- 积分历史列表 -->
			<div v-if="credits.length > 0 && !loading" class="space-y-4">
				<div
					v-for="credit in credits"
					:key="credit.id"
					class="rounded-xl shadow-md overflow-hidden bg-white hover:shadow-lg duration-300 border border-gray-100"
				>
					<div class="p-4">
						<div class="flex items-center justify-between">
							<div class="flex items-center space-x-3">
								<div class="flex-shrink-0">
									<CpuChipIcon class="w-6 h-6 text-blue-500" />
								</div>
								<div class="flex-grow min-w-0">
									<div class="flex items-center space-x-2 mb-1">
										<span class="text-sm font-medium text-gray-600">{{ t("llm_model") }}:</span>
										<span class="text-sm font-semibold text-gray-800 bg-gray-100 px-2 py-1 rounded">
											{{ credit.llm_type }}
										</span>
									</div>
									<div class="text-xs text-gray-500">
										{{ formatDate(credit.created_at) }}
									</div>
								</div>
							</div>
							<div class="flex-shrink-0 text-right">
								<div class="text-lg font-bold text-red-600">
									-{{ formatCredits(credit.credits) }}
								</div>
								<div class="text-xs text-gray-500">{{ t("credits") }}</div>
							</div>
						</div>

						<!-- 详细信息 -->
						<div class="mt-3 pt-3 border-t border-gray-100">
							<div class="grid grid-cols-2 gap-4 text-sm">
								<div class="flex justify-between">
									<span class="text-gray-600">{{ t("input_tokens") }}:</span>
									<span class="font-medium">{{ credit.llm_input.toLocaleString() }}</span>
								</div>
								<div class="flex justify-between">
									<span class="text-gray-600">{{ t("output_tokens") }}:</span>
									<span class="font-medium">{{ credit.llm_output.toLocaleString() }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 无数据状态 -->
			<div v-if="!loading && credits.length == 0" class="text-center py-12">
				<CurrencyDollarIcon class="w-16 h-16 text-gray-300 mx-auto mb-4" />
				<p class="text-xl text-gray-400">
					{{ t("no_credits_history") }}
				</p>
				<p class="text-sm text-gray-500 mt-2">
					{{ t("no_credits_history_desc") }}
				</p>
			</div>

			<!-- 分页 -->
			<div class="mt-6" v-if="creditCount > 0">
				<component_wait>
					<Page
						:page="page"
						:pageSize="pageSize"
						:total="creditCount"
						@change="(e) => (page = e)"
					></Page>
				</component_wait>
			</div>
		</div>
	</div>
</template>

<style scoped>
	.slide-fade-enter-active,
	.slide-fade-leave-active {
		transition: all 0.5s ease;
	}
	.slide-fade-enter-from,
	.slide-fade-leave-to {
		transform: translateX(100%);
		opacity: 0;
	}
</style>
