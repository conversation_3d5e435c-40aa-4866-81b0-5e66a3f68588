<script setup lang="ts">
	import { ref, computed } from "vue";
	import { useUserStore } from "@/stores/user";
	import { createOrder } from "@/api/order";
	import { Error } from "@/utils/notify";
	import { useI18n } from "vue-i18n";
	import { CurrencyDollarIcon, PlusIcon } from "@heroicons/vue/24/outline";

	const userStore = useUserStore();
	const { t } = useI18n();

	const credits = computed(() => userStore.userRef?.credits || 0);
	const isRechargeLoading = ref(false);
	const isRedirecting = ref(false);
	const showRechargeDialog = ref(false);

	const handleRecharge = async () => {
		showRechargeDialog.value = true;
	};

	const confirmRecharge = async () => {
		if (isRechargeLoading.value) return;

		try {
			isRechargeLoading.value = true;
			const response = await createOrder(600, "creem");
			if (response?.payment_url) {
				showRechargeDialog.value = false;
				isRedirecting.value = true;
				window.location.href = response.payment_url;
			} else {
				Error(t("error"), t("payment_error"));
				showRechargeDialog.value = false;
			}
		} catch (err) {
			Error(t("error"), t("payment_error"));
			showRechargeDialog.value = false;
		} finally {
			isRechargeLoading.value = false;
		}
	};
</script>

<template>
	<div class="relative">
		<!-- 支付跳转加载层 -->
		<Teleport to="body">
			<div
				v-if="isRedirecting"
				class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
			>
				<div class="bg-white p-8 rounded-lg text-center">
					<div class="loading loading-spinner loading-lg mb-4"></div>
					<p class="text-lg font-medium">{{ t("redirecting_to_payment") }}</p>
				</div>
			</div>
		</Teleport>

		<!-- 积分余额卡片 -->
		<div
			class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 p-6"
		>
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-4">
					<div
						class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg"
					>
						<CurrencyDollarIcon class="w-6 h-6 text-white" />
					</div>
					<div>
						<p class="text-sm font-medium text-slate-600">
							{{ t("current_credits") }}
						</p>
						<p class="text-3xl font-bold text-slate-800">
							{{ credits.toLocaleString() }}
						</p>
					</div>
				</div>
				<button
					type="button"
					class="btn-gradient-primary gap-2"
					@click="handleRecharge"
				>
					<PlusIcon class="w-4 h-4" />
					{{ t("recharge") }}
				</button>
			</div>
		</div>

		<!-- 充值确认对话框 -->
		<Teleport to="body">
			<div class="modal" :class="{ 'modal-open': showRechargeDialog }">
				<div class="modal-box relative">
					<button
						class="btn btn-sm btn-circle absolute right-2 top-2"
						@click="showRechargeDialog = false"
					>
						✕
					</button>
					<h3 class="font-bold text-lg mb-4">{{ t("recharge_confirmation") }}</h3>
					<div class="bg-blue-50 p-4 rounded-lg mb-4">
						<div class="flex justify-between items-center mb-2">
							<span>{{ t("points_to_add") }}</span>
							<span class="text-xl font-bold text-primary">600</span>
						</div>
						<div class="flex justify-between items-center">
							<span>{{ t("price") }}</span>
							<span class="text-xl font-bold text-primary">$4.99</span>
						</div>
					</div>
					<div class="modal-action">
						<button class="btn btn-ghost" @click="showRechargeDialog = false">
							{{ t("cancel") }}
						</button>
						<button
							class="btn btn-primary"
							@click="confirmRecharge"
							:disabled="isRechargeLoading"
						>
							<span v-if="!isRechargeLoading">{{ t("confirm_payment") }}</span>
							<span v-else class="loading loading-spinner loading-sm"></span>
						</button>
					</div>
				</div>
				<div class="modal-backdrop" @click="showRechargeDialog = false"></div>
			</div>
		</Teleport>
	</div>
</template>
